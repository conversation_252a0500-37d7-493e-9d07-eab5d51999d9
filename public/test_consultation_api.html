<!DOCTYPE html>
<html>
<head>
    <title>Test Consultation API</title>
    <meta name="csrf-token" content="test-token">
</head>
<body>
    <h1>Test Consultation API</h1>
    <button onclick="testFacultyTimeSlots()">Test Faculty Time Slots API</button>
    <div id="results"></div>

    <script>
        async function testFacultyTimeSlots() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Testing API...</p>';
            
            try {
                // Get CSRF token from meta tag
                const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
                
                const url = '/consultation_requests/faculty/1/time_slots?start_date=2025-07-21&end_date=2025-07-27';
                
                console.log('Testing URL:', url);
                console.log('CSRF Token:', csrfToken ? 'Present' : 'Missing');
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': csrfToken || '',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    credentials: 'same-origin'
                });
                
                console.log('Response status:', response.status);
                console.log('Response ok:', response.ok);
                console.log('Response headers:', Object.fromEntries(response.headers.entries()));
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('Success! Data:', data);
                    resultsDiv.innerHTML = `
                        <h3>Success!</h3>
                        <p>Status: ${response.status}</p>
                        <p>Time slots found: ${data.time_slots?.length || 0}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    const errorText = await response.text();
                    console.error('Error response:', errorText);
                    resultsDiv.innerHTML = `
                        <h3>Error</h3>
                        <p>Status: ${response.status}</p>
                        <p>Response: ${errorText}</p>
                    `;
                }
            } catch (error) {
                console.error('Fetch error:', error);
                resultsDiv.innerHTML = `
                    <h3>Fetch Error</h3>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>

import doFetchApi from '@canvas/do-fetch-api-effect'
import type {
  FacultyTimeSlot,
  TimeSlotFormData,
  TimeSlotResponse,
  AvailableDate,
  AvailableDateTime,
  TimeSlotConsultationRequestsResponse,
  ApiError
} from '../types'

const API_BASE = '/faculty_time_slots'

export const fetchTimeSlots = async (): Promise<TimeSlotResponse> => {
  try {
    console.log('-----------------------')
    console.log('FETCHING TIME SLOTS FOR LIST VIEW:')
    console.log('API endpoint:', API_BASE)
    console.log('Current browser time:', new Date().toISOString())
    console.log('User timezone:', Intl.DateTimeFormat().resolvedOptions().timeZone)
    console.log('-----------------------')

    const { json } = await doFetch<PERSON>pi({
      path: API_BASE,
      method: 'GET'
    })

    console.log('-----------------------')
    console.log('TIME SLOTS LIST API RESPONSE:')
    console.log('Total slots received:', json.time_slots?.length || 0)
    if (json.time_slots && json.time_slots.length > 0) {
      console.log('First few slots:')
      json.time_slots.slice(0, 3).forEach((slot: any, index: number) => {
        console.log(`Slot ${index + 1}:`, {
          id: slot.id,
          start_time: slot.start_time,
          end_time: slot.end_time,
          day_of_week: slot.day_of_week,
          specific_date: slot.specific_date,
          is_recurring: slot.is_recurring,
          is_available: slot.is_available
        })
      })
    }
    console.log('-----------------------')

    return json as TimeSlotResponse
  } catch (error: any) {
    console.log('-----------------------')
    console.log('ERROR FETCHING TIME SLOTS LIST:')
    console.log('Error:', error)
    console.log('-----------------------')
    throw new ApiError(error.message || 'Failed to fetch time slots')
  }
}

export const createTimeSlot = async (data: TimeSlotFormData): Promise<FacultyTimeSlot> => {
  try {
    console.log('-----------------------')
    console.log('BEFORE API CALL - CREATE TIME SLOT:')
    console.log('Form data being sent:', data)
    console.log('API endpoint:', API_BASE)
    console.log('Current browser time:', new Date().toISOString())
    console.log('User timezone:', Intl.DateTimeFormat().resolvedOptions().timeZone)
    console.log('-----------------------')

    const { json } = await doFetchApi({
      path: API_BASE,
      method: 'POST',
      body: {
        faculty_time_slot: data
      }
    })

    console.log('-----------------------')
    console.log('AFTER API CALL - CREATE TIME SLOT RESPONSE:')
    console.log('Response data:', json)
    console.log('-----------------------')

    return json as FacultyTimeSlot
  } catch (error: any) {
    console.log('-----------------------')
    console.log('API CALL ERROR - CREATE TIME SLOT:')
    console.log('Error:', error)
    console.log('-----------------------')
    const errorMessage = error.response?.errors?.join(', ') || error.message || 'Failed to create time slot'
    throw new ApiError(errorMessage)
  }
}

export const updateTimeSlot = async (id: string, data: TimeSlotFormData): Promise<FacultyTimeSlot> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/${id}`,
      method: 'PATCH',
      body: {
        faculty_time_slot: data
      }
    })
    return json as FacultyTimeSlot
  } catch (error: any) {
    const errorMessage = error.response?.errors?.join(', ') || error.message || 'Failed to update time slot'
    throw new ApiError(errorMessage)
  }
}

export const deleteTimeSlot = async (id: string): Promise<void> => {
  try {
    await doFetchApi({
      path: `${API_BASE}/${id}`,
      method: 'DELETE'
    })
  } catch (error: any) {
    const errorMessage = error.response?.error || error.message || 'Failed to delete time slot'
    throw new ApiError(errorMessage)
  }
}

export const fetchAvailableDates = async (startDate: string, endDate: string): Promise<AvailableDate[]> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/available_dates`,
      method: 'GET',
      params: {
        start_date: startDate,
        end_date: endDate
      }
    })
    return json.available_dates as AvailableDate[]
  } catch (error: any) {
    throw new ApiError(error.message || 'Failed to fetch available dates')
  }
}

export const fetchAvailableTimes = async (date: string): Promise<AvailableDateTime[]> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/available_times`,
      method: 'GET',
      params: {
        date: date
      }
    })
    return json.available_times as AvailableDateTime[]
  } catch (error: any) {
    throw new ApiError(error.message || 'Failed to fetch available times')
  }
}

export const fetchTimeSlotConsultationRequests = async (timeSlotId: string, datetime: string) => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/${timeSlotId}/consultation_requests`,
      method: 'GET',
      params: {
        datetime: datetime
      }
    })
    return {
      data: json as TimeSlotConsultationRequestsResponse,
      hasError: false
    }
  } catch (error: any) {
    return {
      hasError: true,
      errorMessage: error.message || 'Failed to fetch consultation requests for time slot'
    }
  }
}

// Helper class for API errors
class ApiError extends Error {
  public status?: number
  public errors?: string[]

  constructor(message: string, status?: number, errors?: string[]) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.errors = errors
  }
}

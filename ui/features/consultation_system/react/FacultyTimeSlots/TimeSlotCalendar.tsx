import React, { useState, useEffect, useCallback } from 'react'
import { View } from '@instructure/ui-view'
import { Flex } from '@instructure/ui-flex'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { IconButton } from '@instructure/ui-buttons'
import { Text } from '@instructure/ui-text'
import { Spinner } from '@instructure/ui-spinner'
import CanvasSelect from '@canvas/instui-bindings/react/Select'
import { IconArrowOpenStartLine, IconArrowOpenEndLine } from '@instructure/ui-icons'
import type { FacultyTimeSlot } from '../types'

interface CalendarTimeSlot {
  id: string
  datetime: string
  formatted_time: string
  is_available: boolean
  is_booked: boolean
  has_pending?: boolean
  has_declined?: boolean
  is_past?: boolean
  faculty_time_slot_id: string
  pending_count: number
  approved_count: number
  declined_count: number
  slot_data: FacultyTimeSlot
  created_at: string
  updated_at: string
}

interface TimeSlotCalendarProps {
  currentUserId: string
  onEdit: (slot: FacultyTimeSlot) => void
  onDelete: (id: string) => void
  onAddTimeSlot: (startTime: string, endTime: string, dayOfWeek: string, specificDate?: string) => void
  onViewConsultationRequests: (slot: CalendarTimeSlot) => void
  loading: boolean
  onRefreshReady?: (refreshFn: () => void) => void
}

const TimeSlotCalendar: React.FC<TimeSlotCalendarProps> = ({
  currentUserId,
  onEdit,
  onDelete,
  onAddTimeSlot,
  onViewConsultationRequests,
  loading: externalLoading,
  onRefreshReady
}) => {
  const [currentWeek, setCurrentWeek] = useState(new Date())
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth())
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [timeSlots, setTimeSlots] = useState<CalendarTimeSlot[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Generate week dates
  const getWeekDates = (date: Date) => {
    const week = []
    const startOfWeek = new Date(date)
    const day = startOfWeek.getDay()
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1) // Monday as first day
    startOfWeek.setDate(diff)

    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek)
      day.setDate(startOfWeek.getDate() + i)
      week.push(day)
    }
    return week
  }

  const weekDates = getWeekDates(currentWeek)

  // Navigation functions
  const goToPreviousWeek = () => {
    const prevWeek = new Date(currentWeek)
    prevWeek.setDate(currentWeek.getDate() - 7)
    setCurrentWeek(prevWeek)
  }

  const goToNextWeek = () => {
    const nextWeek = new Date(currentWeek)
    nextWeek.setDate(currentWeek.getDate() + 7)
    setCurrentWeek(nextWeek)
  }

  const goToSelectedMonth = () => {
    const newDate = new Date(selectedYear, selectedMonth, 1)
    setCurrentWeek(newDate)
  }

  // Generate time slots for the day (8 AM to 6 PM in 30-minute intervals)
  const generateTimeSlots = () => {
    const slots = []
    for (let hour = 8; hour < 18; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
        slots.push(timeString)
      }
    }
    return slots
  }

  // Fetch time slots for the current week
  const fetchTimeSlots = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const startDate = weekDates[0].toISOString().split('T')[0]
      const endDate = weekDates[6].toISOString().split('T')[0]
      const url = `/faculty_time_slots/calendar_data?start_date=${startDate}&end_date=${endDate}`

      const response = await fetch(url)

      if (response.ok) {
        const data = await response.json()
        console.log('-----------------------')
        console.log('CALENDAR API RESPONSE - AVAILABLE SLOTS:')
        console.log('URL:', url)
        console.log('Total slots received:', data.time_slots?.length || 0)
        if (data.time_slots && data.time_slots.length > 0) {
          console.log('First few slots:')
          data.time_slots.slice(0, 3).forEach((slot: any, index: number) => {
            console.log(`Slot ${index + 1}:`, {
              datetime: slot.datetime,
              formatted_time: slot.formatted_time,
              is_available: slot.is_available
            })
          })
        }
        console.log('-----------------------')
        setTimeSlots(data.time_slots || [])
      } else {
        const errorText = await response.text()
        console.error('Failed to fetch time slots. Status:', response.status)
        console.error('Error response:', errorText)
        setError('Failed to load calendar data. Please try again.')
      }
    } catch (error) {
      console.error('Error fetching time slots:', error)
      setError('Failed to load calendar data. Please try again.')
    } finally {
      setLoading(false)
    }
  }, [weekDates])

  useEffect(() => {
    fetchTimeSlots()
  }, [currentWeek])

  // Expose refresh function to parent component
  useEffect(() => {
    if (onRefreshReady) {
      onRefreshReady(fetchTimeSlots)
    }
  }, [onRefreshReady, fetchTimeSlots])

  const timeSlotHours = generateTimeSlots()

  // Get slots for specific date and time
  const getSlotsForDateTime = (date: Date, time: string) => {
    const dateStr = date.toISOString().split('T')[0]
    const [hour, minute] = time.split(':')

    const matchingSlots = timeSlots.filter(slot => {
      const slotDate = new Date(slot.datetime)
      const matches = (
        slotDate.toISOString().split('T')[0] === dateStr &&
        slotDate.getHours() === parseInt(hour) &&
        slotDate.getMinutes() === parseInt(minute)
      )

      return matches
    })

    return matchingSlots
  }

  // Handle time slot click - different behavior based on slot status
  const handleTimeSlotClick = (slot: CalendarTimeSlot) => {
    if (slot.has_pending || slot.has_declined) {
      // Show consultation request details for pending/declined slots
      onViewConsultationRequests(slot)
    } else if (slot.slot_data && slot.is_available) {
      // Edit available slots
      onEdit(slot.slot_data)
    }
    // Do nothing for booked or past slots
  }

  // Handle delete slot with recurring options
  const handleDeleteSlot = (slotData: FacultyTimeSlot) => {
    if (slotData.is_recurring) {
      // For recurring slots, provide clear information about what will be deleted
      const confirmMessage =
        `This is a recurring time slot that repeats every ${slotData.day_of_week}.\n\n` +
        `Deleting this slot will remove ALL future instances of this recurring time slot.\n\n` +
        `Are you sure you want to delete this recurring time slot?\n\n` +
        `Note: This action cannot be undone.`

      if (confirm(confirmMessage)) {
        onDelete(slotData.id)
      }
    } else {
      // For non-recurring slots, simple confirmation
      const confirmMessage =
        `Are you sure you want to delete this time slot?\n\n` +
        `Date: ${slotData.specific_date}\n` +
        `Time: ${slotData.start_time} - ${slotData.end_time}\n\n` +
        `Note: This action cannot be undone.`

      if (confirm(confirmMessage)) {
        onDelete(slotData.id)
      }
    }
  }

  // Handle empty slot click for adding new time slot
  const handleEmptySlotClick = (date: Date, time: string) => {
    // Don't allow adding slots in the past
    const slotDateTime = new Date(`${date.toISOString().split('T')[0]}T${time}:00`)
    if (slotDateTime < new Date()) {
      return
    }

    console.log('-----------------------')
    console.log('USER CLICKED ON CALENDAR SLOT:')
    console.log('Selected Date:', date.toISOString().split('T')[0])
    console.log('Selected Time:', time)
    console.log('Combined DateTime:', slotDateTime.toISOString())
    console.log('User Timezone:', Intl.DateTimeFormat().resolvedOptions().timeZone)
    console.log('-----------------------')

    // Calculate end time (30 minutes later)
    const [hour, minute] = time.split(':').map(Number)
    const startTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`

    // Add 30 minutes and handle hour overflow
    let endMinute = minute + 30
    let endHour = hour

    if (endMinute >= 60) {
      endMinute = endMinute - 60
      endHour = endHour + 1
    }

    const endTime = `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`

    // Get day of week
    const dayOfWeek = date.toLocaleDateString('en-US', { weekday: 'long' })

    // Get specific date for non-recurring slots
    const specificDate = date.toISOString().split('T')[0]

    // Call the parent component's add function
    onAddTimeSlot(startTime, endTime, dayOfWeek, specificDate)
  }

  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    })
  }

  // Generate month options
  const monthOptions = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]

  // Generate year options (current year ± 2)
  const currentYear = new Date().getFullYear()
  const yearOptions = []
  for (let year = currentYear - 1; year <= currentYear + 2; year++) {
    yearOptions.push(year)
  }

  return (
    <View as="div" className="faculty-calendar">
      {/* Calendar Navigation */}
      <View as="div" className="calendar-navigation">
        <Flex gap="small" alignItems="center">
          <IconButton
            screenReaderLabel="Previous week"
            onClick={goToPreviousWeek}
            size="small"
          >
            <IconArrowOpenStartLine />
          </IconButton>

          <Text weight="bold" size="large">
            {weekDates[0].toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
          </Text>

          <IconButton
            screenReaderLabel="Next week"
            onClick={goToNextWeek}
            size="small"
          >
            <IconArrowOpenEndLine />
          </IconButton>
        </Flex>

        <Flex gap="small" alignItems="center">
          <View as="div" margin="0 small 0 0">
            <CanvasSelect
              id="month-select"
              label="Month"
              value={selectedMonth.toString()}
              onChange={(_e, value) => {
                setSelectedMonth(parseInt(value))
              }}
            >
              {monthOptions.map((month, index) => (
                <CanvasSelect.Option key={index} id={index.toString()} value={index.toString()}>
                  {month}
                </CanvasSelect.Option>
              ))}
            </CanvasSelect>
          </View>

          <View as="div" margin="0 small 0 0">
            <CanvasSelect
              id="year-select"
              label="Year"
              value={selectedYear.toString()}
              onChange={(_e, value) => setSelectedYear(parseInt(value))}
            >
              {yearOptions.map(year => (
                <CanvasSelect.Option key={year} id={year.toString()} value={year.toString()}>
                  {year.toString()}
                </CanvasSelect.Option>
              ))}
            </CanvasSelect>
          </View>

          <Button onClick={goToSelectedMonth} size="small">
            Go
          </Button>
        </Flex>
      </View>

      {error && (
        <View as="div" margin="0 0 medium 0" padding="small" background="danger">
          <Text color="danger">{error}</Text>
        </View>
      )}

      {/* Calendar Grid */}
      <div className="calendar-grid">
        {/* Week Header */}
        <div className="calendar-header">
          <div className="time-column">Time</div>
          {weekDates.map((date, index) => (
            <div key={index} className="day-column">
              {formatDate(date)}
            </div>
          ))}
        </div>

        {/* Time Slots Grid */}
        {loading || externalLoading ? (
          <View as="div" padding="large" textAlign="center">
            <Spinner renderTitle="Loading time slots..." />
          </View>
        ) : (
          <>
            {timeSlotHours.map((time, timeIndex) => {
              return (
                <div key={timeIndex} className="calendar-row">
                  <div className="time-cell">{time}</div>
                  {weekDates.map((date, dateIndex) => {
                    const slotsAtTime = getSlotsForDateTime(date, time)
                    const hasSlot = slotsAtTime.length > 0
                    const slot = slotsAtTime[0] // Take first slot if multiple
                    const isPast = new Date(`${date.toISOString().split('T')[0]}T${time}:00`) < new Date()

                    // Determine slot status and appearance
                    const getSlotStatus = () => {
                      if (!hasSlot) return {
                        text: isPast ? "Past" : "No Slot",
                        disabled: true,
                        cssClass: isPast ? "past" : "unavailable"
                      }

                      // For past slots, show their actual status but make them non-clickable and dimmed
                      const isPastSlot = slot.is_past || isPast

                      // Check for declined requests first
                      if (slot.declined_count > 0 && !slot.is_booked && !slot.has_pending) {
                        return {
                          text: "Declined",
                          disabled: isPastSlot,
                          cssClass: isPastSlot ? "declined past" : "declined"
                        }
                      }

                      if (slot.is_booked) return {
                        text: "Booked",
                        disabled: isPastSlot,
                        cssClass: isPastSlot ? "approved past" : "approved"
                      }

                      if (slot.has_pending) return {
                        text: "Pending",
                        disabled: isPastSlot,
                        cssClass: isPastSlot ? "pending past" : "pending"
                      }

                      if (slot.is_available) return {
                        text: "Available",
                        disabled: isPastSlot,
                        cssClass: isPastSlot ? "available past" : "available"
                      }

                      return {
                        text: "Unavailable",
                        disabled: true,
                        cssClass: "unavailable"
                      }
                    }

                    const slotStatus = getSlotStatus()

                    return (
                      <div key={dateIndex} className="slot-cell">
                        {hasSlot ? (
                          <>
                            <button
                              className={`slot-button ${slotStatus.cssClass}`}
                              onClick={() => handleTimeSlotClick(slot)}
                              disabled={slotStatus.disabled}
                            >
                              {slotStatus.text}
                            </button>

                            {/* Action buttons for available slots only - exclude past, declined, pending, and booked slots */}
                            {!slotStatus.disabled && slot.slot_data && !slot.is_past && !isPast &&
                             !slot.has_pending && !slot.is_booked &&
                             !(slot.declined_count > 0 && !slot.is_booked && !slot.has_pending) && (
                              <div className="slot-actions">
                                <button
                                  className="action-button"
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    onEdit(slot.slot_data)
                                  }}
                                  title="Edit time slot"
                                >
                                  ✏️
                                </button>
                                <button
                                  className="action-button"
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    handleDeleteSlot(slot.slot_data)
                                  }}
                                  title="Delete time slot"
                                >
                                  🗑️
                                </button>
                              </div>
                            )}
                          </>
                        ) : (
                          <button
                            className={`empty-slot ${isPast ? 'past' : 'clickable'}`}
                            onClick={() => !isPast && handleEmptySlotClick(date, time)}
                            disabled={isPast}
                            title={isPast ? "Cannot add slots in the past" : "Click to add a time slot"}
                          >
                            {isPast ? "Past" : "Add Slot"}
                          </button>
                        )}
                      </div>
                    )
                  })}
                </div>
              )
            })}
          </>
        )}
      </div>
    </View>
  )
}

export default TimeSlotCalendar

# frozen_string_literal: true

class FacultyTimeSlotsController < ApplicationController
  before_action :require_user
  before_action :require_faculty_access
  before_action :set_time_slot, only: [:show, :update, :destroy]

  # GET /faculty_time_slots
  def index
    Rails.logger.info "-----------------------"
    Rails.logger.info "TIME SLOTS LIST API QUERY:"
    Rails.logger.info "Current Time.zone: #{Time.zone.name}"
    Rails.logger.info "Current user timezone: #{@current_user.time_zone}"
    Rails.logger.info "Server time: #{Time.current}"
    Rails.logger.info "-----------------------"

    @time_slots = @current_user.faculty_time_slots.order(:day_of_week, :start_time)

    Rails.logger.info "-----------------------"
    Rails.logger.info "TIME SLOTS LIST API RESPONSE:"
    Rails.logger.info "Total slots found: #{@time_slots.count}"
    if @time_slots.any?
      Rails.logger.info "First few slots:"
      @time_slots.limit(3).each_with_index do |slot, index|
        Rails.logger.info "Slot #{index + 1}: id=#{slot.id}, start_time=#{slot.start_time}, end_time=#{slot.end_time}, day_of_week=#{slot.day_of_week}, specific_date=#{slot.specific_date}, is_recurring=#{slot.is_recurring}, is_available=#{slot.is_available}"
      end
    end
    Rails.logger.info "-----------------------"

    respond_to do |format|
      format.json { render json: time_slots_json(@time_slots) }
      format.html { render_faculty_time_slots_page }
    end
  end

  # GET /faculty_time_slots/:id
  def show
    respond_to do |format|
      format.json { render json: time_slot_json(@time_slot) }
    end
  end

  # POST /faculty_time_slots
  def create
    Rails.logger.info "-----------------------"
    Rails.logger.info "CONTROLLER RECEIVED - CREATE TIME SLOT:"
    Rails.logger.info "Raw params: #{params.inspect}"
    Rails.logger.info "time_slot_params: #{time_slot_params.inspect}"
    Rails.logger.info "Current Time.zone: #{Time.zone.name}"
    Rails.logger.info "Current user timezone: #{@current_user.time_zone}"
    Rails.logger.info "Server time: #{Time.current}"
    Rails.logger.info "-----------------------"

    @time_slot = @current_user.faculty_time_slots.build(time_slot_params)

    Rails.logger.info "-----------------------"
    Rails.logger.info "BEFORE SAVE TO DATABASE:"
    Rails.logger.info "Time slot attributes: #{@time_slot.attributes.inspect}"
    Rails.logger.info "start_time: #{@time_slot.start_time} (#{@time_slot.start_time.class})"
    Rails.logger.info "end_time: #{@time_slot.end_time} (#{@time_slot.end_time.class})"
    Rails.logger.info "specific_date: #{@time_slot.specific_date} (#{@time_slot.specific_date.class})" if @time_slot.specific_date
    Rails.logger.info "-----------------------"

    if @time_slot.save
      Rails.logger.info "-----------------------"
      Rails.logger.info "AFTER SAVE TO DATABASE - SUCCESS:"
      Rails.logger.info "Saved time slot ID: #{@time_slot.id}"
      Rails.logger.info "Saved attributes: #{@time_slot.reload.attributes.inspect}"
      Rails.logger.info "-----------------------"

      respond_to do |format|
        format.json { render json: time_slot_json(@time_slot), status: :created }
      end
    else
      Rails.logger.info "-----------------------"
      Rails.logger.info "SAVE TO DATABASE - FAILED:"
      Rails.logger.info "Validation errors: #{@time_slot.errors.full_messages}"
      Rails.logger.info "-----------------------"

      respond_to do |format|
        format.json { render json: { errors: @time_slot.errors.full_messages }, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /faculty_time_slots/:id
  def update
    if @time_slot.update(time_slot_params)
      respond_to do |format|
        format.json { render json: time_slot_json(@time_slot) }
      end
    else
      respond_to do |format|
        format.json { render json: { errors: @time_slot.errors.full_messages }, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /faculty_time_slots/:id
  def destroy
    if @time_slot.consultation_requests.pending.exists?
      respond_to do |format|
        format.json { 
          render json: { 
            error: 'Cannot delete time slot with pending consultation requests' 
          }, status: :unprocessable_entity 
        }
      end
    else
      @time_slot.destroy
      respond_to do |format|
        format.json { head :no_content }
      end
    end
  end

  # GET /faculty_time_slots/available_dates
  def available_dates
    start_date = Date.parse(params[:start_date]) rescue Date.current
    end_date = Date.parse(params[:end_date]) rescue (start_date + 30.days)

    # Use optimized method to avoid N+1 queries
    available_dates = FacultyTimeSlot.available_dates_for_faculty_in_range(@current_user, start_date, end_date)

    respond_to do |format|
      format.json { render json: { available_dates: available_dates } }
    end
  end

  # GET /faculty_time_slots/available_times
  def available_times
    date = Date.parse(params[:date]) rescue Date.current
    slots = FacultyTimeSlot.available_for_faculty_on_date(@current_user, date)

    available_times = []
    slots.each do |slot|
      slot.available_datetimes_for_date(date).each do |datetime|
        available_times << {
          datetime: datetime.iso8601,
          formatted_time: datetime.strftime('%I:%M %p'),
          slot_id: slot.id
        }
      end
    end

    respond_to do |format|
      format.json { render json: { available_times: available_times.sort_by { |t| t[:datetime] } } }
    end
  end

  # GET /faculty_time_slots/calendar_data
  def calendar_data
    start_date = Date.parse(params[:start_date]) rescue Date.current
    end_date = Date.parse(params[:end_date]) rescue (start_date + 7.days)

    Rails.logger.info "-----------------------"
    Rails.logger.info "CALENDAR DATA API QUERY:"
    Rails.logger.info "Requested start_date: #{params[:start_date]}"
    Rails.logger.info "Requested end_date: #{params[:end_date]}"
    Rails.logger.info "Parsed start_date: #{start_date}"
    Rails.logger.info "Parsed end_date: #{end_date}"
    Rails.logger.info "Current Time.zone: #{Time.zone.name}"
    Rails.logger.info "Current user timezone: #{@current_user.time_zone}"
    Rails.logger.info "-----------------------"

    # Limit date range for performance
    if (end_date - start_date).to_i > 28
      end_date = start_date + 28.days
    end

    date_range = start_date..end_date
    formatted_slots = build_faculty_calendar_slots(@current_user, date_range)

    Rails.logger.info "-----------------------"
    Rails.logger.info "CALENDAR DATA API RESPONSE:"
    Rails.logger.info "Total slots found: #{formatted_slots.length}"
    if formatted_slots.length > 0
      Rails.logger.info "First few slots:"
      formatted_slots.first(3).each_with_index do |slot, index|
        Rails.logger.info "Slot #{index + 1}: datetime=#{slot[:datetime]}, formatted_time=#{slot[:formatted_time]}, available=#{slot[:is_available]}"
      end
    end
    Rails.logger.info "-----------------------"

    respond_to do |format|
      format.json {
        render json: {
          time_slots: formatted_slots.sort_by { |slot| slot[:datetime] },
          debug_info: build_calendar_debug_info(@current_user, date_range, formatted_slots)
        }
      }
    end
  rescue => e
    Rails.logger.error "Error in calendar_data: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    respond_to do |format|
      format.json {
        render json: {
          error: e.message,
          time_slots: [],
          debug_info: { error: true, message: e.message }
        }, status: 500
      }
    end
  end

  # GET /faculty_time_slots/:id/consultation_requests
  def consultation_requests
    Rails.logger.info "Consultation requests params: #{params.inspect}"

    begin
      time_slot = @current_user.faculty_time_slots.find(params[:id])
      datetime = Time.parse(params[:datetime])
      Rails.logger.info "Parsed datetime: #{datetime}"
    rescue ArgumentError => e
      Rails.logger.error "DateTime parse error: #{e.message}"
      respond_to do |format|
        format.json { render json: { error: 'Invalid datetime parameter' }, status: :bad_request }
      end
      return
    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.error "Time slot not found: #{e.message}"
      respond_to do |format|
        format.json { render json: { error: 'Time slot not found' }, status: :not_found }
      end
      return
    end

    # Find consultation requests for this time slot and datetime
    requests = time_slot.consultation_requests.where(
      preferred_datetime: datetime.beginning_of_hour..datetime.end_of_hour
    ).preload(:student).order(:created_at)

    respond_to do |format|
      format.json {
        render json: {
          consultation_requests: requests.map { |request| consultation_request_json(request) },
          time_slot: time_slot_json(time_slot),
          datetime: datetime.iso8601
        }
      }
    end
  rescue => e
    Rails.logger.error "Error in consultation_requests: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    respond_to do |format|
      format.json { render json: { error: 'Internal server error' }, status: :internal_server_error }
    end
  end

  private

  def require_faculty_access
    unless @current_user.teacher_enrollments.active.exists?
      respond_to do |format|
        format.json { render json: { error: 'Access denied. Faculty access required.' }, status: :forbidden }
        format.html {
          flash[:error] = 'Access denied. Faculty access required.'
          redirect_to root_path
        }
      end
    end
  end

  def set_time_slot
    @time_slot = @current_user.faculty_time_slots.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    respond_to do |format|
      format.json { render json: { error: 'Time slot not found' }, status: :not_found }
      format.html {
        flash[:error] = 'Time slot not found'
        redirect_to faculty_time_slots_path
      }
    end
  end

  def time_slot_params
    params.require(:faculty_time_slot).permit(
      :start_time, :end_time, :day_of_week, :is_recurring, 
      :specific_date, :is_available, :notes
    )
  end

  def time_slot_json(time_slot)
    {
      id: time_slot.id,
      start_time: time_slot.start_time.strftime('%H:%M'),
      end_time: time_slot.end_time.strftime('%H:%M'),
      day_of_week: time_slot.day_of_week,
      is_recurring: time_slot.is_recurring,
      specific_date: time_slot.specific_date&.iso8601,
      is_available: time_slot.is_available,
      notes: time_slot.notes,
      created_at: time_slot.created_at.iso8601,
      updated_at: time_slot.updated_at.iso8601,
      pending_requests_count: time_slot.consultation_requests.pending.count
    }
  end

  def time_slots_json(time_slots)
    {
      time_slots: time_slots.map { |slot| time_slot_json(slot) },
      total_count: time_slots.count,
      available_count: time_slots.available.count
    }
  end

  def consultation_request_json(request)
    {
      id: request.id,
      student_name: request.student_name,
      student_id: request.student_number,
      student_department: request.department_program || '',
      preferred_datetime: request.preferred_datetime.iso8601,
      formatted_datetime: request.preferred_datetime.strftime('%A, %B %d, %Y at %I:%M %p'),
      description: request.description,
      nature_of_concern: request.nature_of_concern,
      concern_type_display: request.concern_type_display,
      status: request.status,
      status_display: request.status_display,
      faculty_comment: request.faculty_comment || '',
      created_at: request.created_at.iso8601,
      updated_at: request.updated_at.iso8601,
      can_be_approved: request.can_be_approved?,
      can_be_declined: request.can_be_declined?,
      can_be_completed: request.can_be_completed?,
      college_campus_institute: request.college_campus_institute || '',
      semester: request.semester || '',
      academic_year: request.academic_year || ''
    }
  end

  def render_faculty_time_slots_page
    @page_title = 'Manage Consultation Time Slots'
    js_env({
      FACULTY_TIME_SLOTS: {
        current_user_id: @current_user.id,
        time_slots: time_slots_json(@time_slots)[:time_slots],
        days_of_week: FacultyTimeSlot::DAYS_OF_WEEK
      }
    })

    js_bundle :faculty_time_slots
    css_bundle :consultation_system
  end

  def build_faculty_calendar_slots(faculty, date_range)
    formatted_slots = []

    # Get faculty's time slots (both recurring and specific date slots)
    recurring_slots = faculty.faculty_time_slots.available.recurring
    specific_date_slots = faculty.faculty_time_slots.available.specific_date
                                 .where(specific_date: date_range)

    # Process recurring slots
    formatted_slots.concat(process_recurring_calendar_slots(recurring_slots, date_range, faculty))

    # Process specific date slots
    formatted_slots.concat(process_specific_date_calendar_slots(specific_date_slots, faculty))

    formatted_slots
  end

  def process_recurring_calendar_slots(recurring_slots, date_range, faculty)
    slots = []
    return slots unless recurring_slots.any?

    date_range.each do |date|
      day_name = date.strftime('%A')
      recurring_slots.for_day(day_name).each do |slot|
        # Generate 30-minute intervals for this slot
        current_time = Time.zone.local(date.year, date.month, date.day, slot.start_time.hour, slot.start_time.min)
        end_time = Time.zone.local(date.year, date.month, date.day, slot.end_time.hour, slot.end_time.min)

        while current_time < end_time
          slots << format_calendar_time_slot(slot, current_time, faculty)
          current_time += 30.minutes
        end
      end
    end

    slots
  end

  def process_specific_date_calendar_slots(specific_date_slots, faculty)
    slots = []

    specific_date_slots.each do |slot|
      # Generate 30-minute intervals for this slot
      current_time = Time.zone.local(slot.specific_date.year, slot.specific_date.month, slot.specific_date.day, slot.start_time.hour, slot.start_time.min)
      end_time = Time.zone.local(slot.specific_date.year, slot.specific_date.month, slot.specific_date.day, slot.end_time.hour, slot.end_time.min)

      while current_time < end_time
        slots << format_calendar_time_slot(slot, current_time, faculty)
        current_time += 30.minutes
      end
    end

    slots
  end

  def format_calendar_time_slot(slot, slot_time, faculty)
    is_booked = slot_booked?(slot, slot_time)
    has_pending = slot_has_pending?(slot, slot_time)
    has_declined = slot_has_declined?(slot, slot_time)
    is_past = slot_time < Time.current

    # Get consultation request counts
    pending_count = get_consultation_count(slot, slot_time, 'pending')
    approved_count = get_consultation_count(slot, slot_time, ['approved', 'completed'])
    declined_count = get_consultation_count(slot, slot_time, 'declined')

    {
      id: "#{slot.id}-#{slot_time.to_i}",
      datetime: slot_time.iso8601,
      formatted_time: slot_time.strftime("%I:%M %p"),
      is_available: slot.is_available && !is_booked && !has_pending && !is_past,
      is_booked: is_booked,
      has_pending: has_pending,
      has_declined: has_declined,
      is_past: is_past,
      faculty_time_slot_id: slot.id,
      pending_count: pending_count,
      approved_count: approved_count,
      declined_count: declined_count,
      slot_data: time_slot_json(slot),
      created_at: slot.created_at.iso8601,
      updated_at: slot.updated_at.iso8601
    }
  end

  def get_consultation_count(slot, slot_time, statuses)
    statuses = Array(statuses)
    slot.consultation_requests.where(
      status: statuses,
      preferred_datetime: slot_time.beginning_of_hour..slot_time.end_of_hour
    ).count
  end

  def slot_booked?(slot, slot_time)
    slot.consultation_requests.where(
      status: ['approved', 'completed'],
      preferred_datetime: slot_time.beginning_of_hour..slot_time.end_of_hour
    ).exists?
  end

  def slot_has_pending?(slot, slot_time)
    slot.consultation_requests.where(
      status: 'pending',
      preferred_datetime: slot_time.beginning_of_hour..slot_time.end_of_hour
    ).exists?
  end

  def slot_has_declined?(slot, slot_time)
    slot.consultation_requests.where(
      status: 'declined',
      preferred_datetime: slot_time.beginning_of_hour..slot_time.end_of_hour
    ).exists?
  end

  def build_calendar_debug_info(faculty, date_range, formatted_slots)
    recurring_count = faculty.faculty_time_slots.available.recurring.count
    specific_count = faculty.faculty_time_slots.available.specific_date
                            .where(specific_date: date_range).count

    {
      faculty_id: faculty.id,
      start_date: date_range.first,
      end_date: date_range.last,
      recurring_slots_count: recurring_count,
      specific_date_slots_count: specific_count,
      total_calendar_slots_count: formatted_slots.count
    }
  end
end
